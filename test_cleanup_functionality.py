#!/usr/bin/env python3
"""
Test script to verify cleanup steps functionality
Tests both conditional execution and visual feedback
"""

import json
import os
import sys
import time
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / 'app'))

def create_test_case_with_cleanup(name, has_failing_step=False):
    """Create a test case with cleanup steps for testing"""
    
    # Create a simple test case with some basic actions
    actions = [
        {
            "type": "wait",
            "timestamp": int(time.time() * 1000),
            "duration": 1,
            "action_id": "wait_step_1"
        }
    ]
    
    # Add a failing step if requested
    if has_failing_step:
        actions.append({
            "type": "tap",
            "timestamp": int(time.time() * 1000),
            "locator_type": "xpath",
            "locator_value": "//NonExistentElement[@name='This will fail']",
            "timeout": 5,
            "method": "locator",
            "action_id": "failing_step"
        })
    
    # Add another successful step
    actions.append({
        "type": "wait",
        "timestamp": int(time.time() * 1000),
        "duration": 1,
        "action_id": "wait_step_2"
    })
    
    # Add cleanup steps
    actions.append({
        "type": "cleanupSteps",
        "timestamp": int(time.time() * 1000),
        "test_case_id": "cleanup_test_case",
        "test_case_name": "Cleanup Test Case",
        "test_case_steps": [
            {
                "type": "wait",
                "timestamp": int(time.time() * 1000),
                "duration": 1,
                "action_id": "cleanup_wait_1"
            },
            {
                "type": "wait",
                "timestamp": int(time.time() * 1000),
                "duration": 1,
                "action_id": "cleanup_wait_2"
            },
            {
                "type": "wait",
                "timestamp": int(time.time() * 1000),
                "duration": 1,
                "action_id": "cleanup_wait_3"
            }
        ],
        "action_id": "cleanup_steps_action"
    })
    
    test_case = {
        "name": name,
        "created": time.strftime("%Y-%m-%d %H:%M:%S"),
        "device_id": "test_device",
        "actions": actions,
        "labels": [],
        "updated": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    return test_case

def save_test_case(test_case, filename):
    """Save test case to the test cases directory"""
    test_cases_dir = Path("test_cases")
    test_cases_dir.mkdir(exist_ok=True)
    
    filepath = test_cases_dir / filename
    with open(filepath, 'w') as f:
        json.dump(test_case, f, indent=2)
    
    print(f"Created test case: {filepath}")
    return filepath

def create_test_cases():
    """Create test cases for testing cleanup functionality"""
    
    print("Creating test cases for cleanup functionality testing...")
    
    # Test case 1: All steps pass (cleanup should be skipped)
    test_case_pass = create_test_case_with_cleanup("test_cleanup_skip", has_failing_step=False)
    file_pass = save_test_case(test_case_pass, "test_cleanup_skip.json")
    
    # Test case 2: Has failing step (cleanup should execute)
    test_case_fail = create_test_case_with_cleanup("test_cleanup_execute", has_failing_step=True)
    file_fail = save_test_case(test_case_fail, "test_cleanup_execute.json")
    
    print("\nTest cases created successfully!")
    print("\nTo test the functionality:")
    print("1. Start the app with: python run.py")
    print("2. Connect a device")
    print("3. Load and execute 'test_cleanup_skip' - cleanup should be SKIPPED")
    print("4. Load and execute 'test_cleanup_execute' - cleanup should be EXECUTED with visual feedback")
    print("\nExpected behavior:")
    print("- test_cleanup_skip: Should see 'Skipping X cleanup steps (no failures detected)' in logs")
    print("- test_cleanup_execute: Should see cleanup steps expand with progress indicators")
    
    return file_pass, file_fail

def verify_implementation():
    """Verify that the implementation files have been modified correctly"""
    
    print("Verifying implementation...")
    
    # Check if player.py has been modified
    player_files = [
        Path("app/utils/player.py"),
        Path("app_android/utils/player.py")
    ]
    
    for player_file in player_files:
        if player_file.exists():
            with open(player_file, 'r') as f:
                content = f.read()
                if "has_test_case_failures" in content and "no failures detected" in content:
                    print(f"✓ {player_file} - Conditional cleanup logic implemented")
                else:
                    print(f"✗ {player_file} - Conditional cleanup logic NOT found")
        else:
            print(f"✗ {player_file} - File not found")
    
    # Check if cleanup_steps_action.py has been modified
    cleanup_files = [
        Path("app/actions/cleanup_steps_action.py"),
        Path("app_android/actions/cleanup_steps_action.py")
    ]
    
    for cleanup_file in cleanup_files:
        if cleanup_file.exists():
            with open(cleanup_file, 'r') as f:
                content = f.read()
                if "cleanup_step_result" in content and "socketio.emit" in content:
                    print(f"✓ {cleanup_file} - Visual feedback implemented")
                else:
                    print(f"✗ {cleanup_file} - Visual feedback NOT found")
        else:
            print(f"✗ {cleanup_file} - File not found")
    
    # Check if frontend files have been modified
    frontend_files = [
        Path("app/static/js/app.js"),
        Path("app_android/static/js/app.js")
    ]
    
    for frontend_file in frontend_files:
        if frontend_file.exists():
            with open(frontend_file, 'r') as f:
                content = f.read()
                if "cleanup_step_result" in content and "createCleanupStepsContainer" in content:
                    print(f"✓ {frontend_file} - Frontend visual feedback implemented")
                else:
                    print(f"✗ {frontend_file} - Frontend visual feedback NOT found")
        else:
            print(f"✗ {frontend_file} - File not found")
    
    # Check if CSS files have been modified
    css_files = [
        Path("app/static/css/custom.css"),
        Path("app_android/static/css/custom.css")
    ]
    
    for css_file in css_files:
        if css_file.exists():
            with open(css_file, 'r') as f:
                content = f.read()
                if "cleanup-steps-container" in content and "cleanup-step-executing" in content:
                    print(f"✓ {css_file} - Cleanup steps styles implemented")
                else:
                    print(f"✗ {css_file} - Cleanup steps styles NOT found")
        else:
            print(f"✗ {css_file} - File not found")

if __name__ == "__main__":
    print("=== Cleanup Steps Functionality Test ===\n")
    
    # Verify implementation
    verify_implementation()
    print()
    
    # Create test cases
    create_test_cases()
    
    print("\n=== Test Complete ===")
