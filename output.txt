2025-06-27 09:03:20,249 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-27 09:03:20,249 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-27 09:03:20,250 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-27 09:03:20,250 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-06-27 09:03:20,250 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-06-27 09:03:20,251 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-27 09:03:20,251 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-06-27 09:03:20,252 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-06-27 09:03:20,252 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-06-27 09:03:20,252 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-06-27 09:03:20,253 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-06-27 09:03:20,253 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-06-27 09:03:20,253 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-06-27 09:03:22,319 - __main__ - INFO - Existing processes terminated
2025-06-27 09:03:23,916 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-06-27 09:03:23,971 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-06-27 09:03:24,541 - app - INFO - Using directories from config.py:
2025-06-27 09:03:24,541 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-27 09:03:24,541 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-27 09:03:24,541 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-06-27 09:03:24,545] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-27 09:03:24,546] INFO in database: Test_steps table schema updated successfully
[2025-06-27 09:03:24,546] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-27 09:03:24,547] INFO in database: Screenshots table schema updated successfully
[2025-06-27 09:03:24,547] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-27 09:03:24,547] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-27 09:03:24,547] INFO in database: action_type column already exists in execution_tracking table
[2025-06-27 09:03:24,547] INFO in database: action_params column already exists in execution_tracking table
[2025-06-27 09:03:24,548] INFO in database: action_id column already exists in execution_tracking table
[2025-06-27 09:03:24,548] INFO in database: Successfully updated execution_tracking table schema
[2025-06-27 09:03:24,548] INFO in database: Database initialized successfully
[2025-06-27 09:03:24,548] INFO in database: Checking initial database state...
[2025-06-27 09:03:24,550] INFO in database: Database state: 0 suites, 0 cases, 8383 steps, 1 screenshots, 52 tracking entries
[2025-06-27 09:03:24,551] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-27 09:03:24,551] INFO in database: Test_steps table schema updated successfully
[2025-06-27 09:03:24,551] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-27 09:03:24,552] INFO in database: Screenshots table schema updated successfully
[2025-06-27 09:03:24,552] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-27 09:03:24,552] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-27 09:03:24,553] INFO in database: action_type column already exists in execution_tracking table
[2025-06-27 09:03:24,553] INFO in database: action_params column already exists in execution_tracking table
[2025-06-27 09:03:24,553] INFO in database: action_id column already exists in execution_tracking table
[2025-06-27 09:03:24,553] INFO in database: Successfully updated execution_tracking table schema
[2025-06-27 09:03:24,553] INFO in database: Database initialized successfully
[2025-06-27 09:03:24,553] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-27 09:03:24,554] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-27 09:03:24,554] INFO in database: action_type column already exists in execution_tracking table
[2025-06-27 09:03:24,554] INFO in database: action_params column already exists in execution_tracking table
[2025-06-27 09:03:24,554] INFO in database: action_id column already exists in execution_tracking table
[2025-06-27 09:03:24,554] INFO in database: Successfully updated execution_tracking table schema
[2025-06-27 09:03:24,554] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-27 09:03:24,555] INFO in database: Screenshots table schema updated successfully
[2025-06-27 09:03:24,555] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[2025-06-27 09:03:24,555] INFO in database: Found 52 records in execution_tracking table before clearing
[2025-06-27 09:03:24,556] INFO in database: Successfully cleared execution_tracking table. Removed 52 records.
[2025-06-27 09:03:24,625] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-27 09:03:24,625] INFO in global_values_db: Global values database initialized successfully
[2025-06-27 09:03:24,626] INFO in global_values_db: Using global values from config.py
[2025-06-27 09:03:24,626] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-06-27 09:03:24,670] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-27 09:03:24,684] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11fc2d940>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-06-27 09:03:24,684] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-06-27 09:03:24,719] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-06-27 09:03:24,756] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
