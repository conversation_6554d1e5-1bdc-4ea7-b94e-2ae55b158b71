#!/usr/bin/env python3
"""
Test script to verify the database integration for cleanup steps functionality
"""

import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / 'app'))

def test_has_test_case_failures():
    """Test the has_test_case_failures function"""
    
    try:
        from app.utils.database import has_test_case_failures
        
        print("Testing has_test_case_failures function...")
        
        # Test with non-existent suite (should return False)
        has_failures, details = has_test_case_failures("non_existent_suite", 0)
        print(f"Non-existent suite: has_failures={has_failures}, details={details}")
        
        # Test with force_return_true flag
        has_failures, details = has_test_case_failures("test_suite", 0, force_return_true=True)
        print(f"Force return true: has_failures={has_failures}, details={details}")
        
        print("✓ Database function test completed successfully")
        return True
        
    except ImportError as e:
        print(f"✗ Failed to import database function: {e}")
        return False
    except Exception as e:
        print(f"✗ Error testing database function: {e}")
        return False

def test_cleanup_action_import():
    """Test that cleanup action can be imported correctly"""
    
    try:
        from app.actions.cleanup_steps_action import CleanupStepsAction
        
        print("Testing CleanupStepsAction import...")
        
        # Create an instance (without controller for basic test)
        action = CleanupStepsAction(None)
        print("✓ CleanupStepsAction imported and instantiated successfully")
        return True
        
    except ImportError as e:
        print(f"✗ Failed to import CleanupStepsAction: {e}")
        return False
    except Exception as e:
        print(f"✗ Error testing CleanupStepsAction: {e}")
        return False

def test_android_imports():
    """Test Android-specific imports"""
    
    try:
        sys.path.insert(0, str(Path(__file__).parent / 'app_android'))
        
        from app_android.utils.database import has_test_case_failures as android_has_failures
        from app_android.actions.cleanup_steps_action import CleanupStepsAction as AndroidCleanupAction
        
        print("Testing Android imports...")
        
        # Test Android database function
        has_failures, details = android_has_failures("test_suite", 0)
        print(f"Android database function: has_failures={has_failures}")
        
        # Test Android cleanup action
        action = AndroidCleanupAction(None)
        print("✓ Android imports successful")
        return True
        
    except ImportError as e:
        print(f"✗ Failed to import Android modules: {e}")
        return False
    except Exception as e:
        print(f"✗ Error testing Android modules: {e}")
        return False

def verify_file_modifications():
    """Verify that all necessary files have been modified"""
    
    print("Verifying file modifications...")
    
    modifications = [
        ("app/utils/player.py", ["has_test_case_failures", "no failures detected"]),
        ("app_android/utils/player.py", ["has_test_case_failures", "no failures detected"]),
        ("app/actions/cleanup_steps_action.py", ["cleanup_step_result", "socketio.emit"]),
        ("app_android/actions/cleanup_steps_action.py", ["cleanup_step_result", "socketio.emit"]),
        ("app/static/js/app.js", ["cleanup_step_result", "createCleanupStepsContainer"]),
        ("app_android/static/js/app.js", ["cleanup_step_result", "createCleanupStepsContainer"]),
        ("app/static/css/custom.css", ["cleanup-steps-container", "cleanup-step-executing"]),
        ("app_android/static/css/custom.css", ["cleanup-steps-container", "cleanup-step-executing"])
    ]
    
    all_good = True
    
    for file_path, required_strings in modifications:
        file_path = Path(file_path)
        if file_path.exists():
            with open(file_path, 'r') as f:
                content = f.read()
                
            missing = [s for s in required_strings if s not in content]
            if missing:
                print(f"✗ {file_path} - Missing: {missing}")
                all_good = False
            else:
                print(f"✓ {file_path} - All modifications present")
        else:
            print(f"✗ {file_path} - File not found")
            all_good = False
    
    return all_good

if __name__ == "__main__":
    print("=== Database Integration Test ===\n")
    
    success = True
    
    # Test file modifications
    if not verify_file_modifications():
        success = False
    
    print()
    
    # Test database function
    if not test_has_test_case_failures():
        success = False
    
    print()
    
    # Test cleanup action import
    if not test_cleanup_action_import():
        success = False
    
    print()
    
    # Test Android imports
    if not test_android_imports():
        success = False
    
    print()
    
    if success:
        print("✓ All integration tests passed!")
        print("\nImplementation Summary:")
        print("1. ✓ Conditional cleanup execution implemented")
        print("2. ✓ Visual feedback for cleanup steps implemented")
        print("3. ✓ Socket events for step-by-step progress implemented")
        print("4. ✓ Frontend UI components for cleanup steps implemented")
        print("5. ✓ CSS styles for cleanup steps implemented")
        print("6. ✓ Test cases created for verification")
        
        print("\nNext Steps:")
        print("1. Start the app: python run.py")
        print("2. Connect a device")
        print("3. Test with 'test_cleanup_skip' (should skip cleanup)")
        print("4. Test with 'test_cleanup_execute' (should show cleanup with visual feedback)")
    else:
        print("✗ Some integration tests failed!")
    
    print("\n=== Integration Test Complete ===")
